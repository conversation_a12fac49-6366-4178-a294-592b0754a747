import { BasicColumn } from '/@/components/Table';
import { h } from 'vue';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
import { getTextByCode } from '/@/components/Form/src/utils/areaDataUtil';
import { getCompanyHandle, getUserCompany } from '/@/api/common/api';

export const columns: BasicColumn[] = [
  {
    title: '资产项目（资产名称）',
    dataIndex: 'name',
    width: 200,
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    fixed: 'left',
    customRender: ({ text }) => {
      return render.renderDict(text, 'record_status');
    },
  },
  {
    title: '资产编号',
    dataIndex: 'code',
    width: 180,
  },
  {
    title: '企业自定义编号',
    dataIndex: 'enterpriseCode',
    width: 150,
  },
  {
    title: '所属集团',
    dataIndex: 'groupName',
    width: 240,
    customRender: ({ _text }) => {
      return '厦门市城市建设发展投资有限公司';
    },
  },
  {
    title: '所属企业',
    dataIndex: 'companyName',
    width: 240,
  },
  {
    title: '权属单位名称',
    dataIndex: 'ownUnit',
    width: 150,
  },
  {
    title: '管理单位',
    dataIndex: 'manageUnit',
    width: 240,
  },
  {
    title: '是否报送国资委',
    dataIndex: 'reportOrNot',
    width: 150,
    customRender: ({ text }) => {
      return render.renderDict(text, 'yes_no');
    },
  },
  {
    title: '资产位置',
    dataIndex: 'location',
    width: 200,
    customRender: ({ record }) => {
      return record.province ? `${getTextByCode(record.province)} ${getTextByCode(record.city)} ${getTextByCode(record.area)}` : '';
    },
  },
  {
    title: '详细地址',
    dataIndex: 'address',
    width: 200,
  },
  {
    title: '土地用途（运营方式和情况）',
    dataIndex: 'useType',
    width: 200,
    customRender: ({ text }) => {
      return render.renderDict(text, 'land_use_type');
    },
  },
  {
    title: '具体的土地用途',
    dataIndex: 'useTypeInput',
    width: 150,
  },
  {
    title: '土地建设情况',
    dataIndex: 'construction',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(text, 'land_construction');
    },
  },
  {
    title: '土地获得时间',
    dataIndex: 'gainDate',
    width: 120,
  },
  {
    title: '土地性质',
    dataIndex: 'gainType',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(text, 'land_gain_type');
    },
  },
  {
    title: '土地取得价格(万元)',
    dataIndex: 'landPrice',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '地价款（租金）欠缴金(万元)',
    dataIndex: 'landArrears',
    width: 200,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '土地总面积(㎡)',
    dataIndex: 'landArea',
    width: 130,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '可租面积(㎡)',
    dataIndex: 'rentableArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '产权面积(㎡)',
    dataIndex: 'propertyArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '非产权面积(㎡)',
    dataIndex: 'notPropertyArea',
    width: 130,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '资产原值(万元)',
    dataIndex: 'assetsAmount',
    width: 130,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '账面价值(万元)',
    dataIndex: 'bookAmount',
    width: 130,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '账面价值时点',
    dataIndex: 'dateOfBookValue',
    width: 120,
  },
  {
    title: '是否有产权证',
    dataIndex: 'propertyType',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(text, 'land_property_type');
    },
  },
  {
    title: '产权证获得日期',
    dataIndex: 'warrantDate',
    width: 130,
  },
  {
    title: '代管委托方',
    dataIndex: 'custodyEntrustingParty',
    width: 120,
  },
  {
    title: '是否房地权证合一',
    dataIndex: 'warrantIntegration',
    width: 150,
    customRender: ({ text }) => {
      return render.renderDict(text, 'yes_no');
    },
  },
  {
    title: '土地权证(含英文)',
    dataIndex: 'landWarrant',
    width: 150,
  },
  {
    title: '是否账外',
    dataIndex: 'offAccount',
    width: 80,
    customRender: ({ text }) => {
      return render.renderDict(text, 'yes_no');
    },
  },
  {
    title: '是否投保',
    dataIndex: 'insuranceOrNot',
    width: 80,
    customRender: ({ text }) => {
      return render.renderDict(text, 'yes_no');
    },
  },
  {
    title: '是否抵押或质押',
    dataIndex: 'mortgageOrNot',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(text, 'yes_no');
    },
  },
  {
    title: '是否竣工财务结算办理',
    dataIndex: 'completionOrNot',
    width: 180,
    customRender: ({ text }) => {
      return render.renderDict(text, 'yes_no');
    },
  },
  {
    title: '是否拖欠工程款',
    dataIndex: 'owingOrNot',
    width: 140,
    customRender: ({ text }) => {
      return render.renderDict(text, 'yes_no');
    },
  },
  {
    title: '是否具有盘活价值',
    dataIndex: 'vitalizeOrNot',
    width: 150,
    customRender: ({ text }) => {
      return render.renderDict(text, 'yes_no');
    },
  },
  {
    title: '工作进展',
    dataIndex: 'workProgress',
    width: 200,
  },
  {
    title: '存在问题',
    dataIndex: 'problems',
    width: 200,
  },
  {
    title: '资产使用状态',
    dataIndex: 'assetsStatus',
    width: 150,
    customRender: ({ text }) => {
      const textArray = text ? text.split(',') : [];
      return h(
        'div',
        textArray.map((item) => {
          const finalText = render.renderDict(item, 'land_assets_status', true);
          return h('span', finalText);
        })
      );
    },
  },
  {
    title: '闲置面积(㎡)',
    dataIndex: 'idleArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '自用面积(㎡)',
    dataIndex: 'useArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '出租面积(㎡)',
    dataIndex: 'rentArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '出借面积(㎡)',
    dataIndex: 'lendArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '占用面积(㎡)',
    dataIndex: 'occupyArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '转让面积(㎡)',
    dataIndex: 'sellArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '其他面积(㎡)',
    dataIndex: 'otherArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
  },
  {
    title: '经办人',
    dataIndex: 'operator',
    width: 120,
  },
  {
    title: '录入人',
    dataIndex: 'entryClerk',
    width: 120,
  },
  {
    title: '录入时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 150,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '资产编号',
    field: 'code',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产编号',
    },
  },
  {
    label: '资产名称',
    field: 'name',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产名称',
    },
  },
  {
    label: '状态',
    field: 'status',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择状态',
      dictCode: 'record_status',
    },
  },
  {
    label: '所属企业',
    field: 'companyName',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择所属企业',
      api: getUserCompany,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
  },
  {
    label: '资产位置',
    field: 'assetsLocation',
    component: 'JAreaLinkage',
    componentProps: {
      placeholder: '请选择省/市/区',
      showArea: true,
      showAll: false,
      saveCode: 'all',
    },
  },
  {
    label: '土地用途',
    field: 'useType',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择土地用途',
      dictCode: 'land_use_type',
    },
  },
  {
    label: '土地性质',
    field: 'gainType',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择土地性质',
      dictCode: 'land_gain_type',
    },
  },
  {
    label: '是否有产权证',
    field: 'propertyType',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择是否有产权证',
      dictCode: 'land_property_type',
    },
  },
  {
    label: '使用状态',
    field: 'assetsStatus',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择资产使用状态',
      dictCode: 'land_assets_status',
    },
  },
  {
    label: '是否账外',
    field: 'offAccount',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择是否账外',
      dictCode: 'yes_no',
    },
  },
  {
    label: '是否投保',
    field: 'insuranceOrNot',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择是否投保',
      dictCode: 'yes_no',
    },
  },
  {
    label: '是否抵押或质押',
    field: 'mortgageOrNot',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择是否抵押或质押',
      dictCode: 'yes_no',
    },
  },
  {
    label: '土地面积',
    field: 'landArea',
    component: 'JRangeNumber',
    componentProps: {
      placeholder: '最小值',
      min: 0,
      precision: 2,
      style: { width: '100%' },
    },
  },
  {
    label: '管理单位',
    field: 'manageUnit',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择管理单位',
      api: getCompanyHandle,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
  },
  {
    label: '土地建设情况',
    field: 'construction',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择土地建设情况',
      mode: 'multiple',
      dictCode: 'land_construction',
    },
  },
  {
    label: '经办人',
    field: 'operator',
    component: 'Input',
    componentProps: {
      placeholder: '请输入经办人',
    },
  },
  {
    label: '录入时间',
    field: 'createTime',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '更新时间',
    field: 'updateTime',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '是否报送国资委',
    field: 'reportOrNot',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
  {
    label: '是否竣工财务结算办理',
    field: 'completionOrNot',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
  {
    label: '是否拖欠工程款',
    field: 'owingOrNot',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
  {
    label: '是否具有盘活价值',
    field: 'vitalizeOrNot',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
];

// 盘活记录表格列配置
export const dealRecordColumns = [
  {
    title: '日期',
    dataIndex: 'date',
    key: 'date',
    width: 120,
  },
  {
    title: '是否已盘活',
    dataIndex: 'isResult',
    key: 'isResult',
    width: 100,
  },
  {
    title: '盘活方式',
    dataIndex: 'vitalizeType',
    key: 'vitalizeType',
    width: 180,
  },
  {
    title: '已采取的盘活管理措施',
    dataIndex: 'reason',
    key: 'reason',
    width: 300,
  },
  {
    title: '下一步建议',
    dataIndex: 'nextReason',
    key: 'nextReason',
    width: 300,
  },
  {
    title: '操作',
    key: 'action',
    width: 80,
    fixed: 'right',
  },
];
