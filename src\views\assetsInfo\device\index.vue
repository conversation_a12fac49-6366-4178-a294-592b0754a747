<template>
  <div>
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleCreate">新增</a-button>
        <import-modal @success="importSuccess" exportTemplateName="下载导入模板" :exportTemplateUrl="downloadTemplate" :importUrl="importExcel" />
        <a-button
          type="primary"
          preIcon="ant-design:export-outlined"
          @click="handleExport"
          :loading="exportLoading"
          :disabled="selectedRowKeys.length === 0"
          >导出</a-button
        >
        <a-button type="primary" preIcon="ant-design:download-outlined" @click="handleExportAll">全部导出</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
      <template #summary>
        <a-table-summary>
          <!-- 当页合计 -->
          <a-table-summary-row>
            <a-table-summary-cell align="right" :index="0" :col-span="3">当前页合计</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="4" :col-span="15" />
            <a-table-summary-cell align="right" :index="16" :col-span="1">{{ sumMap.deviceAmountCurrentSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="17" :col-span="1">{{ sumMap.totalDepreciationCurrentSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="18" :col-span="2" />
            <a-table-summary-cell align="right" :index="20" :col-span="1">{{ sumMap.bookAmountCurrentSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="22" :col-span="6" />
            <a-table-summary-cell align="right" :index="29" />
          </a-table-summary-row>
          <!-- 合计 -->
          <a-table-summary-row>
            <a-table-summary-cell align="right" :index="0" :col-span="3">合计</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="4" :col-span="15" />
            <a-table-summary-cell align="right" :index="16" :col-span="1">{{ sumMap.deviceAmountSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="17" :col-span="1">{{ sumMap.totalDepreciationSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="18" :col-span="2" />
            <a-table-summary-cell align="right" :index="20" :col-span="1">{{ sumMap.bookAmountSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="22" :col-span="6" />
            <a-table-summary-cell align="right" :index="29" />
          </a-table-summary-row>
        </a-table-summary>
      </template>
    </BasicTable>

    <!-- 导入模态框 -->
    <!-- <ImportModal @register="registerImportModal" @success="handleImportSuccess" /> -->
  </div>
</template>

<script lang="ts" name="device-list" setup>
  import { ref } from 'vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useRouter } from 'vue-router';
  import { columns, searchFormSchema } from './device.data';
  import { list, deleteDevice, exportExcel, importExcel, downloadTemplate, getSum } from './device.api';
  import { mapTableTotalSummary } from '/@/utils/common/compUtils';
  import { useMethods } from '/@/hooks/system/useMethods';
  import ImportModal from '/@/components/ImportModal/index.vue';
  const { createMessage } = useMessage();

  const router = useRouter();
  const { handleExportXls } = useMethods();
  const exportLoading = ref(false);

  const sumMap = ref({});

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'device-list',
    tableProps: {
      title: '设备信息列表',
      api: list,
      columns: columns,
      size: 'small',
      canResize: false,
      // 显示序号列
      showIndexColumn: true,
      // 显示表格设置
      showTableSetting: true,
      // 表格设置配置
      tableSetting: {
        // 缓存键名
        cacheKey: 'device_list',
        // 显示列设置
        setting: true,
        // 显示刷新按钮
        redo: false,
        // 显示尺寸调整
        size: false,
        // 显示全屏按钮
        fullScreen: false,
      },
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 140,
        // 显示展开/收起按钮
        showAdvancedButton: true,
        // 超过3列时默认折叠
        autoAdvancedCol: 4,
        baseColProps: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 6,
          xl: 6,
          xxl: 6,
        },
        fieldMapToTime: [
          ['createTime', ['createTimeMin', 'createTimeMax'], 'YYYY-MM-DD'],
          ['updateTime', ['updateTimeMin', 'updateTimeMax'], 'YYYY-MM-DD'],
        ],
      },
      actionColumn: {
        width: 160,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        if (params.deviceAmount) {
          const [min, max] = params.deviceAmount.split(',');
          params.deviceAmountMin = min;
          params.deviceAmountMax = max;
        }
        if (params.bookAmount) {
          const [min, max] = params.bookAmount.split(',');
          params.bookAmountMin = min;
          params.bookAmountMax = max;
        }
        if (params.assetsLocation) {
          console.log(params.assetsLocation, 'params.assetsLocation');
          const [province, city, area] = params.assetsLocation.split(',');
          params.province = province;
          params.city = city;
          params.area = area;
        }
        console.log(params, 'params');
        getSumHandle(params);
        return params;
      },
      pagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '50', '100'],
      },
    },
  });

  // 注册table数据
  const [registerTable, { reload, getForm }, { rowSelection, selectedRowKeys }] = tableContext;

  function getSumHandle(params) {
    getSum(params).then((res) => {
      // console.log(res, 'res');
      sumMap.value = res;
    });
  }
  /**
   * 新增事件
   */
  function handleCreate() {
    router.push('/assetsInfo/device/add');
  }

  /**
   * 编辑事件
   */
  async function handleEdit(record: Recordable) {
    router.push(`/assetsInfo/device/edit/${record.id}`);
  }

  /**
   * 删除事件
   */
  async function handleDelete(record: Recordable) {
    await deleteDevice({ id: record.id }, reload);
  }

  /**
   * 导出选中数据
   */
  async function handleExport() {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warning('请至少选择一条数据');
      return;
    }
    const formData = getForm();
    formData.ids = selectedRowKeys.value.join();
    exportLoading.value = true;
    await handleExportXls('设备信息列表', exportExcel, formData, 'post')
      .then((res) => {
        console.log(res, 'res');
      })
      .finally(() => {
        exportLoading.value = false;
      });
  }

  /**
   * 导出全部数据
   */
  async function handleExportAll() {
    const formData = getForm();
    exportLoading.value = true;
    await handleExportXls('设备信息列表', exportExcel, formData, 'post')
      .then((res) => {
        console.log(res, 'res');
      })
      .finally(() => {
        exportLoading.value = false;
      });
  }

  /**
   * 操作栏
   */
  function getTableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped>
  @import './index.less';
</style> 