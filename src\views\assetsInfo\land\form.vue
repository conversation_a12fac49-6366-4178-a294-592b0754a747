<template>
  <div class="land-form">
    <div class="p-4">
      <a-form ref="formRef" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <!-- 基本信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
              基本信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="资产编号" name="code">
                  <a-input v-model:value="formData.code" placeholder="保存后系统自动生成" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="企业自定义编号"
                  name="enterpriseCode"
                  :rules="[{ required: true, message: '请输入企业自定义编号', trigger: 'blur' }]"
                >
                  <a-input v-model:value="formData.enterpriseCode" placeholder="请输入企业自定义编号" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="name" :rules="[{ required: true, message: '请输入资产项目（资产名称）', trigger: 'blur' }]">
                  <template #label>
                    资产项目（资产名称）
                    <a-tooltip title="系统内要求资产名称唯一">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input v-model:value="formData.name" placeholder="请输入资产项目（资产名称）" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="所属集团" name="groupName">
                  <JDictSelectTag
                    v-model:value="formData.groupName"
                    :showChooseOption="false"
                    placeholder="请选择所属集团"
                    disabled
                    dictCode="group_name"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="所属企业" name="companyName" :rules="[{ required: true, message: '请选择所属企业', trigger: 'change' }]">
                  <ApiSelect
                    v-model:value="formData.companyName"
                    placeholder="请选择所属企业"
                    :api="getUserCompany"
                    :transResult="(originResult) => originResult.map((item) => ({ value: item.id, label: item.name }))"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="权属单位名称" name="ownUnit" :rules="[{ required: true, message: '请输入权属单位名称', trigger: 'blur' }]">
                  <a-input v-model:value="formData.ownUnit" placeholder="请输入权属单位名称" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="资产位置" name="assetsLocation" :rules="[{ required: true, message: '请选择省/市/区', trigger: 'change' }]">
                  <JAreaLinkage
                    v-model:value="formData.assetsLocation"
                    placeholder="请选择省/市/区"
                    :showArea="true"
                    :showAll="false"
                    saveCode="all"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="详细地址" name="address">
                  <a-input v-model:value="formData.address" placeholder="请输入详细地址" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="status" :rules="[{ required: true, message: '请选择状态', trigger: 'change' }]">
                  <template #label>
                    状态
                    <a-tooltip title="备案数据支持撤回、草稿数据和撤回数据支持作废">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-select v-model:value="formData.status" placeholder="请选择状态">
                    <a-select-option :value="0" :disabled="initStatus === '1' || initStatus === '2' || initStatus === '4'">草稿</a-select-option>
                    <a-select-option :value="1" :disabled="initStatus === '2' || initStatus === '4'">备案</a-select-option>
                    <a-select-option :value="2" :disabled="initStatus === '-1' || initStatus === '0' || initStatus === '4'">撤回</a-select-option>
                    <a-select-option :value="4" :disabled="initStatus === '-1' || initStatus === '0' || initStatus === '1'">作废</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="管理单位" name="manageUnit" :rules="[{ required: true, message: '请选择管理单位', trigger: 'change' }]">
                  <ApiSelect
                    v-model:value="formData.manageUnit"
                    placeholder="请选择管理单位"
                    :api="getCompanyHandle"
                    :transResult="(originResult) => originResult.map((item) => ({ value: item.id, label: item.name }))"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否报送国资委" name="reportOrNot" :rules="[{ required: true, message: '请选择', trigger: 'change' }]">
                  <JDictSelectTag v-model:value="formData.reportOrNot" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="经办人" name="operator" :rules="[{ required: true, message: '请输入经办人', trigger: 'blur' }]">
                  <a-input v-model:value="formData.operator" placeholder="请输入经办人" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="录入人" name="entryClerk" :rules="[{ required: true, message: '请输入录入人', trigger: 'blur' }]">
                  <a-input v-model:value="formData.entryClerk" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="录入时间" name="createTime" :rules="[{ required: true, message: '请输入录入时间', trigger: 'blur' }]">
                  <a-input v-model:value="formData.createTime" disabled />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 土地信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:map-outlined" class="title-icon" />
              土地信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item
                  label="土地用途（运营方式和情况）"
                  :labelCol="{ span: 12 }"
                  name="useType"
                  :rules="[{ required: true, message: '请选择土地用途（运营方式和情况）', trigger: 'change' }]"
                >
                  <JDictSelectTag v-model:value="formData.useType" :showChooseOption="false" dictCode="land_use_type" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="土地建设情况" name="construction" :rules="[{ required: true, message: '请选择土地建设情况', trigger: 'change' }]">
                  <JDictSelectTag v-model:value="formData.construction" :showChooseOption="false" dictCode="land_construction" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="土地获得时间" name="gainDate" :rules="[{ required: true, message: '请选择土地获得时间', trigger: 'change' }]">
                  <a-date-picker
                    v-model:value="formData.gainDate"
                    placeholder="请选择土地获得时间"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item
                  label="具体的土地用途"
                  name="useTypeInput"
                  :rules="[{ required: formData.useType === '3', message: '请填写具体的土地用途', trigger: 'blur' }]"
                >
                  <a-input v-model:value="formData.useTypeInput" placeholder="请填写具体的土地用途" :disabled="formData.useType !== '3'" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="土地性质" name="gainType" :rules="[{ required: true, message: '请选择土地性质', trigger: 'change' }]">
                  <JDictSelectTag v-model:value="formData.gainType" :showChooseOption="false" dictCode="land_gain_type" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="土地取得价格(万元)" name="landPrice">
                  <a-input-number
                    v-model:value="formData.landPrice"
                    placeholder="请输入土地取得价格"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item :labelCol="{ span: 12 }" label="地价款（租金）欠缴金(万元)" name="landArrears">
                  <a-input-number
                    v-model:value="formData.landArrears"
                    placeholder="请输入地价款（租金）欠缴金"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="landArea" :rules="[{ required: true, message: '请输入土地总面积', trigger: 'blur' }]">
                  <template #label>
                    土地总面积(㎡)
                    <a-tooltip title="总面积符合以下公式：（1）总面积=产权面积+非产权面积（2）总面积=可租面积+自用面积+占用面积+借用面积">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.landArea"
                    placeholder="请输入土地总面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="rentableArea" :rules="[{ required: true, message: '请输入可租面积', trigger: 'blur' }]">
                  <template #label>
                    可租面积(㎡)
                    <a-tooltip title="可租面积符合以下公式：（1）可租面积=空置、闲置面积+出租面积（2）没有产权但是有对外出租的，也属于可租面积">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.rentableArea"
                    placeholder="请输入可租面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="产权面积(㎡)" name="propertyArea" :rules="[{ required: true, message: '请输入产权面积', trigger: 'blur' }]">
                  <a-input-number
                    v-model:value="formData.propertyArea"
                    placeholder="请输入产权面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="非产权面积(㎡)"
                  name="notPropertyArea"
                  :rules="[{ required: true, message: '请输入非产权面积', trigger: 'blur' }]"
                >
                  <a-input-number
                    v-model:value="formData.notPropertyArea"
                    placeholder="请输入非产权面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="资产原值(万元)" name="assetsAmount" :rules="[{ required: true, message: '请输入资产原值', trigger: 'blur' }]">
                  <a-input-number
                    v-model:value="formData.assetsAmount"
                    placeholder="请输入资产原值"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="账面价值(万元)" name="bookAmount" :rules="[{ required: true, message: '请输入账面价值', trigger: 'blur' }]">
                  <a-input-number
                    v-model:value="formData.bookAmount"
                    placeholder="请输入账面价值"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="账面价值时点"
                  name="dateOfBookValue"
                  :rules="[{ required: true, message: '请选择账面价值时点', trigger: 'change' }]"
                >
                  <a-date-picker
                    v-model:value="formData.dateOfBookValue"
                    placeholder="请选择账面价值时点"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否有产权证" name="propertyType" :rules="[{ required: true, message: '请选择是否有产权证', trigger: 'change' }]">
                  <JDictSelectTag v-model:value="formData.propertyType" :showChooseOption="false" dictCode="land_property_type" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item
                  label="产权证获得日期"
                  name="warrantDate"
                  :rules="[{ required: formData.propertyType === '1', message: '请选择产权证获得日期', trigger: 'change' }]"
                >
                  <a-date-picker
                    v-model:value="formData.warrantDate"
                    placeholder="请选择产权证获得日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled="formData.propertyType === '0'"
                    :disabled-date="disabledDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="代管委托方"
                  name="custodyEntrustingParty"
                  :rules="[{ required: formData.propertyType === '2', message: '请输入代管委托方', trigger: 'blur' }]"
                >
                  <a-input v-model:value="formData.custodyEntrustingParty" placeholder="请输入代管委托方" :disabled="formData.propertyType !== '2'" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="是否房地权证合一"
                  name="warrantIntegration"
                  :rules="[{ required: true, message: '请选择是否房地权证合一', trigger: 'change' }]"
                >
                  <JDictSelectTag v-model:value="formData.warrantIntegration" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="土地权证(含英文)" name="landWarrant">
                  <a-input v-model:value="formData.landWarrant" placeholder="请输入土地权证(含英文)" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否账外" name="offAccount" :rules="[{ required: true, message: '请选择是否账外', trigger: 'change' }]">
                  <JDictSelectTag v-model:value="formData.offAccount" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否投保" name="insuranceOrNot" :rules="[{ required: true, message: '请选择是否投保', trigger: 'change' }]">
                  <JDictSelectTag v-model:value="formData.insuranceOrNot" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item
                  label="是否抵押或质押"
                  name="mortgageOrNot"
                  :rules="[{ required: true, message: '请选择是否抵押或质押', trigger: 'change' }]"
                >
                  <JDictSelectTag v-model:value="formData.mortgageOrNot" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否竣工财务结算办理" name="completionOrNot">
                  <JDictSelectTag v-model:value="formData.completionOrNot" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否拖欠工程款" name="owingOrNot">
                  <JDictSelectTag v-model:value="formData.owingOrNot" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="是否具有盘活价值" name="vitalizeOrNot" :rules="[{ required: true, message: '请选择', trigger: 'change' }]">
                  <JDictSelectTag v-model:value="formData.vitalizeOrNot" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item
                  label="工作进展"
                  :labelCol="{ span: 3 }"
                  name="workProgress"
                  :rules="[{ required: true, message: '请输入工作进展', trigger: 'blur' }]"
                >
                  <a-textarea v-model:value="formData.workProgress" placeholder="请输入工作进展" :rows="4" :maxlength="300" show-count />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item
                  label="存在问题"
                  :labelCol="{ span: 3 }"
                  name="problems"
                  :rules="[{ required: true, message: '请输入存在问题', trigger: 'blur' }]"
                >
                  <a-textarea v-model:value="formData.problems" placeholder="请输入存在问题" :rows="4" :maxlength="300" show-count />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 资产使用情况 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:pie-chart-outlined" class="title-icon" />
              资产使用情况
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item
                  :labelCol="{ span: 3 }"
                  name="assetsStatus"
                  :rules="[{ required: true, message: '请选择资产使用状态', trigger: 'change' }]"
                >
                  <template #label>
                    资产使用状态
                    <a-tooltip title="选择'闲置'状态时，将显示闲置信息填写区域">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <JDictSelectTag v-model:value="formData.assetsStatus" mode="multiple" :showChooseOption="false" dictCode="land_assets_status" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="idleArea">
                  <template #label>
                    空置闲置面积(㎡)
                    <a-tooltip title="空置天数180天(含)以内的数据属于【空置信息】，空置天数180天以上的数据属于【闲置信息】">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.idleArea"
                    placeholder="请输入空置闲置面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    :disabled="!formData.assetsStatus.includes('0')"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="useArea">
                  <template #label>
                    自用面积(㎡)
                    <a-tooltip title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.useArea"
                    placeholder="请输入自用面积"
                    :precision="2"
                    :min="0"
                    :disabled="!formData.assetsStatus.includes('1')"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="rentArea">
                  <template #label>
                    出租面积(㎡)
                    <a-tooltip title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.rentArea"
                    placeholder="请输入出租面积"
                    :precision="2"
                    :min="0"
                    :disabled="!formData.assetsStatus.includes('2')"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="lendArea">
                  <template #label>
                    出借面积(㎡)
                    <a-tooltip title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.lendArea"
                    placeholder="请输入出借面积"
                    :precision="2"
                    :min="0"
                    :disabled="!formData.assetsStatus.includes('3')"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="occupyArea">
                  <template #label>
                    占用面积(㎡)
                    <a-tooltip title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.occupyArea"
                    placeholder="请输入占用面积"
                    :precision="2"
                    :min="0"
                    :disabled="!formData.assetsStatus.includes('4')"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="sellArea">
                  <template #label>
                    转让面积(㎡)
                    <a-tooltip title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.sellArea"
                    placeholder="请输入转让面积"
                    :precision="2"
                    :min="0"
                    :disabled="!formData.assetsStatus.includes('6')"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="otherArea">
                  <template #label>
                    其他面积(㎡)
                    <a-tooltip title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.otherArea"
                    placeholder="请输入其他面积"
                    :precision="2"
                    :min="0"
                    :disabled="!formData.assetsStatus.includes('7')"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item :labelCol="{ span: 3 }" label="备注" name="remark">
                  <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :rows="4" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 资产闲置信息（仅当勾选"闲置"时显示） -->
        <div v-if="showIdleInfo" class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:bed-outlined" class="title-icon" />
              资产闲置信息
            </div>
            <div class="form-card-action">
              <a-switch v-model:checked="showRevitalizationInfo" checked-children="添加闲置信息" un-checked-children="不添加" />
            </div>
          </div>
          <div class="form-card-body" v-if="showRevitalizationInfo">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item
                  label="闲置起始日期"
                  :name="['bizLeaveEntity', 'startDate']"
                  :rules="[{ required: true, message: '请选择闲置起始日期', trigger: 'change' }]"
                >
                  <a-date-picker
                    v-model:value="formData.bizLeaveEntity.startDate"
                    placeholder="请选择闲置起始日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledStartDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="闲置结束日期"
                  :name="['bizLeaveEntity', 'endDate']"
                  :rules="[{ validator: validateIdleEndDate, trigger: 'change' }]"
                >
                  <a-date-picker
                    v-model:value="formData.bizLeaveEntity.endDate"
                    placeholder="请选择闲置结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledEndDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item>
                  <template #label>
                    闲置天数
                    <a-tooltip title="空置天数180天(含)以内的数据属于【空置信息】，空置天数180天以上的数据属于【闲置信息】">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <span>{{ idleDays }} 天</span>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="空置闲置面积(㎡)" :name="['bizLeaveEntity', 'idleArea']">
                  <a-input v-model:value="formData.bizLeaveEntity.idleArea" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="闲置资产原值(万元)" :name="['bizLeaveEntity', 'assetsAmount']">
                  <a-input-number
                    v-model:value="formData.bizLeaveEntity.assetsAmount"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入闲置资产原值"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="闲置资产账面价值(万元)"
                  :name="['bizLeaveEntity', 'bookAmount']"
                  :rules="[{ required: true, message: '请输入闲置资产账面价值', trigger: 'blur' }]"
                >
                  <a-input-number
                    v-model:value="formData.bizLeaveEntity.bookAmount"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入闲置资产账面价值"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item
                  label="账面价值时点"
                  :name="['bizLeaveEntity', 'dateOfBookValue']"
                  :rules="[{ required: true, message: '请选择账面价值时点', trigger: 'change' }]"
                >
                  <a-date-picker
                    v-model:value="formData.bizLeaveEntity.dateOfBookValue"
                    placeholder="请选择账面价值时点"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item
                  :labelCol="{ span: 3 }"
                  label="闲置原因"
                  :name="['bizLeaveEntity', 'idleReason']"
                  :rules="[{ required: true, message: '请输入闲置原因', trigger: 'blur' }]"
                >
                  <a-textarea v-model:value="formData.bizLeaveEntity.idleReason" :rows="2" placeholder="请输入闲置原因" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item :labelCol="{ span: 3 }" :name="['bizLeaveEntity', 'remark']" label="备注">
                  <a-textarea v-model:value="formData.bizLeaveEntity.remark" :rows="2" placeholder="请输入备注" />
                </a-form-item>
              </a-col>
            </a-row>

            <!-- 盘活记录表格 -->
            <a-row style="margin-top: 20px;">
              <a-col :span="24">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                  <span style="font-weight: bold;">盘活记录</span>
                  <a-button type="primary" @click="addRevitalizationRecord">
                    <PlusOutlined />
                    新增记录
                  </a-button>
                </div>

                <a-table :data-source="formData.bizLeaveEntity.dealList" :pagination="false" bordered size="small">
                  <a-table-column title="日期" width="180">
                    <template #default="{ record, index }">
                      <a-form-item
                        :name="['bizLeaveEntity', 'dealList', index, 'dealDate']"
                        :rules="[{ required: true, message: '请选择日期', trigger: 'change' }]"
                        class="record-table-form-item"
                      >
                        <a-date-picker
                          v-model:value="record.dealDate"
                          placeholder="请选择日期"
                          format="YYYY-MM-DD"
                          value-format="YYYY-MM-DD"
                          style="width: 100%"
                        />
                      </a-form-item>
                    </template>
                  </a-table-column>
                  <a-table-column title="是否已盘活" width="150">
                    <template #default="{ record, index }">
                      <a-form-item
                        :name="['bizLeaveEntity', 'dealList', index, 'isResult']"
                        :rules="[{ required: true, message: '请选择是否已盘活', trigger: 'change' }]"
                        class="record-table-form-item"
                      >
                        <JDictSelectTag v-model:value="record.isResult" :showChooseOption="false" dictCode="yes_no" />
                      </a-form-item>
                    </template>
                  </a-table-column>
                  <a-table-column title="盘活方式" width="180">
                    <template #default="{ record, index }">
                      <a-form-item
                        :name="['bizLeaveEntity', 'dealList', index, 'vitalizeType']"
                        :rules="[{ required: true, message: '请选择盘活方式', trigger: 'change' }]"
                        class="record-table-form-item"
                      >
                        <JDictSelectTag v-model:value="record.vitalizeType" :showChooseOption="false" dictCode="vitalize_type" />
                      </a-form-item>
                    </template>
                  </a-table-column>
                  <a-table-column title="已采取的盘活管理措施" width="300">
                    <template #default="{ record, index }">
                      <a-form-item :name="['bizLeaveEntity', 'dealList', index, 'reason']" class="record-table-form-item">
                        <a-textarea v-model:value="record.reason" placeholder="请输入已采取的盘活管理措施" :rows="2" />
                      </a-form-item>
                    </template>
                  </a-table-column>
                  <a-table-column title="下一步建议" width="300">
                    <template #default="{ record, index }">
                      <a-form-item :name="['bizLeaveEntity', 'dealList', index, 'nextReason']" class="record-table-form-item">
                        <a-textarea v-model:value="record.nextReason" placeholder="请输入下一步建议" :rows="2" />
                      </a-form-item>
                    </template>
                  </a-table-column>
                  <a-table-column title="操作" width="80" fixed="right">
                    <template #default="{ index }">
                      <a-button type="primary" danger size="small" @click="removeRevitalizationRecord(index)">
                        删除
                      </a-button>
                    </template>
                  </a-table-column>
                </a-table>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 表单操作按钮 -->
        <div class="form-footer">
          <a-button type="primary" @click="handleSubmit" :loading="loading"> 提交 </a-button>
          <a-button @click="handleReset" style="margin-left: 12px"> 重置 </a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" name="LandForm" setup>
  import { ref, onMounted, computed, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useUserStore } from '/@/store/modules/user';
  import { Icon } from '/@/components/Icon';
  import { JDictSelectTag, JAreaLinkage, ApiSelect } from '/@/components/Form';
  import { QuestionCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getCompanyHandle, getUserCompany } from '/@/api/common/api'
  import { saveOrUpdate, getDetail } from './land.api';
  import dayjs from 'dayjs';

  const user = useUserStore().getUserInfo;
  console.log(user, 'user');
  // 定义盘活记录接口
  interface RevitalizationRecord {
    dealDate: string;
    isResult: string;
    vitalizeType: string;
    reason: string;
    nextReason: string;
  }

  // 定义闲置信息接口
  interface IdleInfo {
    startDate: string;
    endDate: string;
    idleArea: number;
    assetsAmount: number;
    bookAmount: number;
    dateOfBookValue: string;
    idleReason: string;
    remark: string;
    dealList: RevitalizationRecord[];
  }

  const route = useRoute();
  const router = useRouter();
  const { createMessage, createConfirm } = useMessage();
  const loading = ref(false);

  const showRevitalizationInfo = ref(false);

  // 获取路由参数判断是新增还是编辑
  const isUpdate = ref(false);
  const recordId = ref('');

  const initStatus = ref('-1');

  // 表单引用
  const formRef = ref();

  // 表单数据
  const formData = ref<any>({
    // 基本信息
    id: '',
    code: '',
    enterpriseCode: '',
    name: '',
    groupName: '0',
    companyName: '',
    ownUnit: '',
    assetsLocation: '',
    address: '',
    status: 0,
    manageUnit: '',
    reportOrNot: '',
    operator: '',
    entryClerk: '',
    createTime: '',
    // 土地信息
    useType: '',
    useTypeInput: '',
    construction: '',
    gainDate: '',
    gainType: '',
    landPrice: 0,
    landArrears: 0,
    landArea: 0,
    rentableArea: 0,
    propertyArea: 0,
    notPropertyArea: 0,
    assetsAmount: 0,
    bookAmount: 0,
    dateOfBookValue: '',
    propertyType: '',
    warrantDate: '',
    custodyEntrustingParty: '',
    warrantIntegration: '',
    landWarrant: '',
    offAccount: '',
    insuranceOrNot: '',
    mortgageOrNot: '',
    completionOrNot: '',
    owingOrNot: '',
    vitalizeOrNot: '',
    workProgress: '',
    problems: '',
    // 资产使用情况
    assetsStatus: [],
    idleArea: 0,
    useArea: 0,
    rentArea: 0,
    lendArea: 0,
    occupyArea: 0,
    sellArea: 0,
    otherArea: 0,
    remark: '',
    bizLeaveEntity: {
      startDate: '',
      endDate: '',
      assetsAmount: 0,
      bookAmount: 0,
      dateOfBookValue: '',
      idleReason: '',
      remark: '',
      idleArea: 0,
      dealList: [],
    },
  });

  // 闲置信息相关
  const showIdleInfo = ref(false);
  const idleInfo = ref<IdleInfo>({
    startDate: '',
    endDate: '',
    assetsAmount: 0,
    bookAmount: 0,
    dateOfBookValue: '',
    idleReason: '',
    remark: '',
    idleArea: 0,
    dealList: [],
  });

  // 计算闲置天数
  const idleDays = computed(() => {
    if (idleInfo.value.startDate && idleInfo.value.endDate) {
      const start = dayjs(idleInfo.value.startDate);
      const end = dayjs(idleInfo.value.endDate);
      const days = end.diff(start, 'day');
      return days > 0 ? days : 0;
    }
    return 0;
  });

  // 日期禁用函数
  const disabledDate = (current: dayjs.Dayjs) => {
    return current && current > dayjs().endOf('day');
  };

  const disabledStartDate = (current: dayjs.Dayjs) => {
    return current && current > dayjs().endOf('day');
  };

  const disabledEndDate = (current: dayjs.Dayjs) => {
    if (!idleInfo.value.startDate) return false;
    return current && current <= dayjs(idleInfo.value.startDate);
  };

  // 闲置结束日期验证
  const validateIdleEndDate = (rule: any, value: string) => {
    if (value && idleInfo.value.startDate) {
      if (dayjs(value).isSame(dayjs(idleInfo.value.startDate)) || dayjs(value).isBefore(dayjs(idleInfo.value.startDate))) {
        return Promise.reject('闲置结束日期应大于起始日期');
      }
    }
    return Promise.resolve();
  };

  // 添加盘活记录
  const addRevitalizationRecord = () => {
    formData.value.bizLeaveEntity.dealList.push({
      dealDate: '',
      isResult: '',
      vitalizeType: '',
      reason: '',
      nextReason: '',
    });
  };

  // 删除盘活记录
  const removeRevitalizationRecord = (index: number) => {
    formData.value.bizLeaveEntity.dealList.splice(index, 1);
  };

  /**
   * 初始化
   */
  onMounted(async () => {
    // 判断是新增还是编辑
    const { id } = route.params;
    if (id) {
      isUpdate.value = true;
      recordId.value = id as string;
      await loadDetail();
    } else {
      isUpdate.value = false;
      initStatus.value = '-1'
      // 设置默认值
      Object.assign(formData.value, {
        reportOrNot: '',
        status: 0,
        groupName: '0',
        insuranceOrNot: '',
        useType: '',
        construction: '',
        gainType: '',
        propertyType: '',
        warrantIntegration: '',
        offAccount: '',
        mortgageOrNot: '',
        vitalizeOrNot: '',
        assetsStatus: [],
        entryClerk: user.realname,
        createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        bizLeaveEntity: {
          startDate: '',
          endDate: '',
          assetsAmount: 0,
          bookAmount: 0,
          dateOfBookValue: '',
          idleReason: '',
          remark: '',
          idleArea: 0,
          dealList: [],
        },
      });
    }
  });

  /**
   * 加载详情
   */
  async function loadDetail() {
    try {
      const data = await getDetail(recordId.value);
      if (data) {
        Object.assign(formData.value, {
          ...data,
          assetsLocation: `${data.province},${data.city},${data.area}`,
          assetsStatus: data.assetsStatus.split(','),
          groupName: `${data.groupName}`,
          reportOrNot: `${data.reportOrNot}`,
          useType: `${data.useType}`,
          construction: `${data.construction}`,
          gainType: `${data.gainType}`,
          propertyType: `${data.propertyType}`,
          warrantIntegration: `${data.warrantIntegration}`,
          offAccount: `${data.offAccount}`,
          insuranceOrNot: `${data.insuranceOrNot}`,
          mortgageOrNot: `${data.mortgageOrNot}`,
          completionOrNot: `${data.completionOrNot}`,
          owingOrNot: `${data.owingOrNot}`,
          vitalizeOrNot: `${data.vitalizeOrNot}`,
        });

        initStatus.value = `${data.status}`;
        console.log('data.assetsStatus', data.assetsStatus);
        // 处理闲置信息
        if (data.assetsStatus && data.assetsStatus.includes('0')) {
          showIdleInfo.value = true;
          if (data.bizLeaveEntity && data.bizLeaveEntity.remark) {
            showRevitalizationInfo.value = true;
          }
        }
      }
    } catch (error) {
      createMessage.error('加载详情失败');
    }
  }

  /**
   * 提交表单
   */
  async function handleSubmit() {
    try {
      // 验证表单
      await formRef.value.validate();

      // 合并表单数据
      const submitData = {
        ...formData.value,
        assetsStatus: formData.value.assetsStatus.join(','),
      };
      console.log(submitData, 'submitData');
      if (submitData.assetsLocation && Array.isArray(submitData.assetsLocation)) {
        console.log(submitData.assetsLocation, 'submitData.assetsLocation');
        const [province, city, area] = submitData.assetsLocation;
        console.log(province, city, area, 'province, city, area');
        submitData.province = province;
        submitData.city = city;
        submitData.area = area;
      }
      loading.value = true;
      await saveOrUpdate(submitData, isUpdate.value);

      createMessage.success(isUpdate.value ? '更新成功' : '新增成功');
      router.push('/assetsInfo/land');
    } catch (error) {
      console.log(error, 'error');
      createMessage.error(isUpdate.value ? '更新失败' : '新增失败');
    } finally {
      loading.value = false;
    }
  }

  /**
   * 重置表单
   */
  function handleReset() {
    createConfirm({
      title: '确认重置',
      content: '是否确认重置表单数据？',
      iconType: 'warning',
      onOk: () => {
        if (isUpdate.value) {
          loadDetail();
        } else {
          // 重置表单数据
          Object.assign(formData.value, {
            reportOrNot: 0,
            status: 0,
            groupName: '0',
            insuranceOrNot: 0,
            useType: '',
            construction: '',
            gainType: '',
            propertyType: '',
            warrantIntegration: 0,
            offAccount: 0,
            mortgageOrNot: 0,
            vitalizeOrNot: 0,
            assetsStatus: [],
          });
          showIdleInfo.value = false;
          idleInfo.value = {
            startDate: '',
            endDate: '',
            assetsAmount: 0,
            bookAmount: 0,
            dateOfBookValue: '',
            idleReason: '',
            remark: '',
            idleArea: 0,
            dealList: [],
          };
        }
      },
    });
  }

  // 监听资产使用状态变化
  watch(
    () => formData.value.assetsStatus,
    (newStatus) => {
      if (newStatus && newStatus.includes('0')) {
        showIdleInfo.value = true;
        // 自动带出数据
        idleInfo.value.assetsAmount = formData.value.assetsAmount || 0;
        idleInfo.value.bookAmount = formData.value.bookAmount || 0;
        idleInfo.value.dateOfBookValue = formData.value.dateOfBookValue || '';
        idleInfo.value.idleArea = formData.value.idleArea || 0;
      } else {
        showIdleInfo.value = false;
      }
    }
  );
</script>

<style lang="less" scoped>
  .land-form {
    .simple-title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
      padding: 16px 24px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
    }

    .form-card {
      background: white;
      border-radius: 8px;
      margin-bottom: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .form-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
        border-bottom: 1px solid #f0f0f0;

        .form-card-title {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 500;
          color: #333;

          .title-icon {
            margin-right: 8px;
            color: #1890ff;
          }
        }

        .form-card-action {
          .ant-btn {
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }
      }

      .form-card-body {
        padding: 24px;
      }
    }

    .form-footer {
      text-align: center;
      padding: 24px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    .help-text {
      font-size: 12px;
      color: #909399;
      line-height: 1.5;
      margin-top: 5px;
    }

    .empty-text {
      color: #909399;
      text-align: center;
      padding: 20px 0;
    }

    // 统一设置表单项标签宽度
    :deep(.ant-form-item-label) {
      width: 180px;
      min-width: 180px;
      text-align: right;
      padding-right: 8px;
    }

    :deep(.ant-form-item-label > label) {
      width: 100%;
      justify-content: flex-end;
    }
  }
</style>