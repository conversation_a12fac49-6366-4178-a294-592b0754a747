import { defHttp } from '/@/utils/http/axios';
import { Modal } from 'ant-design-vue';

enum Api {
  list = '/biz/house/queryPage',
  save = '/mock/house/add',
  edit = '/mock/house/edit',
  delete = '/mock/house/delete',
  deleteBatch = '/mock/house/deleteBatch',
  importExcel = '/mock/house/importExcel',
  exportXls = '/mock/house/exportXls',
  exportAll = '/mock/house/exportAll',
  downloadTemplate = '/biz/house/downloadImportTemplate',
  detail = '/biz/house/detail/',
  sum = '/biz/house/querySum',
}

export const exportExcel = Api.exportXls;
export const downloadTemplate = Api.downloadTemplate;
export const importExcel = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.post({ url: Api.list, params });

/**
 * 获取总和
 * @param params
 */
export const getSum = (params) => defHttp.post({ url: Api.sum, params });

/**
 * 保存或更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

/**
 * 获取详情
 * @param id
 */
export const getDetail = (id) => defHttp.get({ url: Api.detail + id, params: { id } });

/**
 * 删除
 * @param params
 * @param handleSuccess
 */
export const deleteHouse = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.delete, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDeleteHouse = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 导出
 * @param params
 */
export const exportHouse = (params) => defHttp.get({ url: Api.exportXls, params, responseType: 'blob' });

/**
 * 全部导出
 * @param params
 */
export const exportAllHouse = (params) => defHttp.get({ url: Api.exportAll, params, responseType: 'blob' }); 